"""
Main FastAPI application entry point.
"""

from fastapi import FastAPI
from fastapi.responses import RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
import nltk

from src.helper.logger import setup_new_logging
from src.core.activity_log import UserActivityMiddleware
from src.core.exceptions import add_exception_handlers
from main_routes import include_routers, nltk_download
from src.v3 import v3_app

# Initialize logger
loggers = setup_new_logging(__name__)
loggers.info("Application starting")

# Import and create a lifespan context manager for the application
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for the FastAPI application.
    This is the recommended way to handle startup and shutdown events.
    """
    # Startup: Download NLTK data asynchronously
    loggers.info("Starting NLTK data download in application startup")
    await nltk_download()
    loggers.info("NLTK data download completed")

    # Yield control back to FastAPI
    yield

    # Shutdown: Clean up resources if needed
    loggers.info("Application shutting down")

# Create FastAPI application with lifespan context manager
app = FastAPI(
    title="Eko Backend",
    description="Eko AI API",
    version="0.1.0",
    lifespan=lifespan,
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/docs/oauth2-redirect",
    openapi_url="/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow WebSocket connections from this origin
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Note: Referrer checking for login requests is handled via a custom dependency
# in src/models/security.py (get_login_form_with_referrer_check) which is used
# in the login endpoint to set client_id based on a flexible referrer configuration.
# The configuration maps client_ids to lists of referrer domains, allowing multiple
# domains to be associated with each client_id.


# Add user activity tracking middleware
app.add_middleware(
    UserActivityMiddleware,
    exclude_paths=["/docs", "/openapi.json", "/redoc"],  # Exclude documentation paths
    auto_error=False  # Don't raise errors for missing tokens
)

# Include all routers from the main_routes module
include_routers(app)

# Mount v3 API as a sub-application
app.mount("/v3", v3_app)

# Add exception handlers from the core.exceptions module
add_exception_handlers(app)


# Custom OpenAPI schema generation to ensure correct OAuth2 configuration
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Fix OAuth2 security scheme for Swagger UI
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}
    if "securitySchemes" not in openapi_schema["components"]:
        openapi_schema["components"]["securitySchemes"] = {}

    # Update the OAuth2PasswordBearer scheme to work with Swagger UI
    openapi_schema["components"]["securitySchemes"]["OAuth2PasswordBearer"] = {
        "type": "oauth2",
        "flows": {
            "password": {
                "tokenUrl": "/login",
                "scopes": {}
            }
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

# Root endpoint redirects to API documentation
@app.get("/")
async def root():
    return RedirectResponse(url="/docs")


# Custom docs endpoints
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """Custom Swagger UI for Echo Bot."""
    from fastapi.openapi.docs import get_swagger_ui_html
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title="Echo Bot API - Documentation",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        oauth2_redirect_url="/docs/oauth2-redirect",
    )


@app.get("/docs/oauth2-redirect", include_in_schema=False)
async def swagger_ui_redirect():
    """OAuth2 redirect endpoint for Swagger UI authentication."""
    from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
    return get_swagger_ui_oauth2_redirect_html()

# Health check endpoint for load balancer
@app.get("/health")
async def health_check():
    """Health check endpoint for load balancer and monitoring."""
    return {
        "status": "healthy",
        "service": "eko-backend",
        "version": "0.1.0"
    }

# Automatically apply logging to all routes

loggers.info("Application started successfully")