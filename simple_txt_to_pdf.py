#!/usr/bin/env python3
"""
Simple text to PDF converter using fpdf2 library.
This script converts a text file to a properly formatted PDF document.
"""

try:
    from fpdf import FPDF
    FPDF_AVAILABLE = True
except ImportError:
    FPDF_AVAILABLE = False

import os
import sys

class TextToPDF:
    def __init__(self, font_size=10, line_height=5):
        if not FPDF_AVAILABLE:
            raise ImportError("fpdf2 library is required. Install it with: pip install fpdf2")
        
        self.pdf = FPDF()
        self.font_size = font_size
        self.line_height = line_height
        
    def convert_file(self, txt_file_path, pdf_file_path):
        """Convert text file to PDF"""
        
        # Check if input file exists
        if not os.path.exists(txt_file_path):
            raise FileNotFoundError(f"Input file not found: {txt_file_path}")
        
        # Read the text file
        try:
            with open(txt_file_path, "r", encoding="utf-8") as f:
                content = f.read()
        except UnicodeDecodeError:
            # Try with different encoding if UTF-8 fails
            with open(txt_file_path, "r", encoding="latin-1") as f:
                content = f.read()
        
        # Add a page
        self.pdf.add_page()
        
        # Set font
        self.pdf.set_font("Courier", size=self.font_size)  # Using Courier for monospace
        
        # Split content into lines
        lines = content.split('\n')
        
        for line in lines:
            # Check if we need a new page
            if self.pdf.get_y() > 270:  # Near bottom of page
                self.pdf.add_page()
            
            # Handle long lines by wrapping them
            if len(line) > 80:  # Wrap long lines
                while len(line) > 80:
                    self.pdf.cell(0, self.line_height, line[:80], ln=True)
                    line = line[80:]
                if line:  # Print remaining part
                    self.pdf.cell(0, self.line_height, line, ln=True)
            else:
                self.pdf.cell(0, self.line_height, line, ln=True)
        
        # Save the PDF
        self.pdf.output(pdf_file_path)
        print(f"✅ PDF created successfully: {pdf_file_path}")
        
        # Display file information
        if os.path.exists(pdf_file_path):
            file_size = os.path.getsize(pdf_file_path)
            print(f"📊 Output file size: {file_size:,} bytes")
            print(f"📄 Converted {len(lines)} lines from {txt_file_path}")

def install_fpdf():
    """Try to install fpdf2 library"""
    try:
        import subprocess
        print("📦 Installing fpdf2 library...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "fpdf2"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ fpdf2 installed successfully!")
            return True
        else:
            print(f"❌ Failed to install fpdf2: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing fpdf2: {e}")
        return False

def fallback_conversion(txt_file_path, pdf_file_path):
    """Fallback method using HTML to PDF conversion"""
    print("🔄 Using fallback method...")
    
    # Read the text file
    with open(txt_file_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # Create HTML content
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Converted Document</title>
        <style>
            body {{
                font-family: 'Courier New', monospace;
                font-size: 10px;
                line-height: 1.2;
                margin: 20px;
                white-space: pre-wrap;
            }}
        </style>
    </head>
    <body>
{content}
    </body>
    </html>
    """
    
    # Save as HTML first
    html_file = pdf_file_path.replace('.pdf', '.html')
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"📄 HTML version created: {html_file}")
    print("💡 You can open this HTML file in a browser and print to PDF")
    print("💡 Or use a tool like wkhtmltopdf to convert HTML to PDF")

def main():
    """Main function"""
    
    # Default file paths
    input_file = "ddd.Txt"
    output_file = "ddd.pdf"
    
    # Check command line arguments
    if len(sys.argv) >= 3:
        input_file = sys.argv[1]
        output_file = sys.argv[2]
    elif len(sys.argv) == 2:
        input_file = sys.argv[1]
        output_file = os.path.splitext(input_file)[0] + ".pdf"
    
    try:
        if not FPDF_AVAILABLE:
            print("⚠️  fpdf2 library not found.")
            if input("Install fpdf2? (y/n): ").lower().startswith('y'):
                if install_fpdf():
                    # Restart the script after installation
                    os.execv(sys.executable, [sys.executable] + sys.argv)
                else:
                    fallback_conversion(input_file, output_file)
                    return
            else:
                fallback_conversion(input_file, output_file)
                return
        
        # Convert using fpdf2
        converter = TextToPDF()
        converter.convert_file(input_file, output_file)
        
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print(f"💡 Make sure the file '{input_file}' exists in the current directory")
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        print("🔄 Trying fallback method...")
        fallback_conversion(input_file, output_file)

if __name__ == "__main__":
    main()
