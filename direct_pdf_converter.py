#!/usr/bin/env python3
"""
Direct text to PDF converter using fpdf2.
This script converts the bank statement text file to PDF.
"""

import os
import sys

def convert_txt_to_pdf(input_file, output_file):
    """Convert text file to PDF using fpdf2"""
    
    try:
        from fpdf import FPDF
    except ImportError:
        print("❌ fpdf2 library not found. Please install it with: pip3 install fpdf2")
        return False
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    # Read the text file
    try:
        with open(input_file, "r", encoding="utf-8") as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(input_file, "r", encoding="latin-1") as f:
                content = f.read()
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return False
    
    # Create PDF
    pdf = FPDF()
    pdf.add_page()
    
    # Set font - using Courier for monospace (good for bank statements)
    pdf.set_font("Courier", size=8)  # Smaller font for bank statement
    
    # Split content into lines
    lines = content.split('\n')
    
    line_height = 3.5  # Smaller line height for more content per page
    
    for i, line in enumerate(lines):
        # Check if we need a new page (leaving some margin at bottom)
        if pdf.get_y() > 280:
            pdf.add_page()
        
        # Handle very long lines by truncating or wrapping
        max_chars = 120  # Adjust based on font size and page width
        
        if len(line) > max_chars:
            # Split long lines
            while len(line) > max_chars:
                pdf.cell(0, line_height, line[:max_chars], ln=True)
                line = line[max_chars:]
                
                # Check for new page again
                if pdf.get_y() > 280:
                    pdf.add_page()
            
            # Print remaining part if any
            if line.strip():
                pdf.cell(0, line_height, line, ln=True)
        else:
            # Print normal line
            pdf.cell(0, line_height, line, ln=True)
    
    # Save the PDF
    try:
        pdf.output(output_file)
        print(f"✅ PDF created successfully: {output_file}")
        
        # Display file information
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"📊 Output file size: {file_size:,} bytes")
            print(f"📄 Converted {len(lines)} lines from {input_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
        return False

def main():
    """Main function"""
    
    input_file = "ddd.Txt"
    output_file = "ddd.pdf"
    
    # Check command line arguments
    if len(sys.argv) >= 3:
        input_file = sys.argv[1]
        output_file = sys.argv[2]
    elif len(sys.argv) == 2:
        input_file = sys.argv[1]
        output_file = os.path.splitext(input_file)[0] + ".pdf"
    
    print(f"🔄 Converting {input_file} to {output_file}...")
    
    success = convert_txt_to_pdf(input_file, output_file)
    
    if success:
        print("🎉 Conversion completed successfully!")
    else:
        print("❌ Conversion failed!")

if __name__ == "__main__":
    main()
