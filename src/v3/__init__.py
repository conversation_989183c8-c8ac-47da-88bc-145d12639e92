from src.v3.chat.routes import v3_reply_router
# from src.v3.chat.agent_sdk_tool.api import agent_router
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

v3_app = FastAPI(
    title="Echo Bot API v3",
    description="Version 3 of the Echo Bot API",
    version="3.0.0",
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/v3/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    servers=[
        {
            "url": "/v3",
            "description": "V3 API Service"
        }
    ]
)

def custom_openapi():
    if v3_app.openapi_schema:
        return v3_app.openapi_schema

    openapi_schema = get_openapi(
        title=v3_app.title,
        version=v3_app.version,
        description=v3_app.description,
        routes=v3_app.routes,
    )

    # Ensure the server URL is correctly set
    openapi_schema["servers"] = [
        {
            "url": "/v3",
            "description": "V3 API Service"
        }
    ]

    # Fix OAuth2 security scheme for Swagger UI
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}
    if "securitySchemes" not in openapi_schema["components"]:
        openapi_schema["components"]["securitySchemes"] = {}

    # Update the OAuth2PasswordBearer scheme to work with Swagger UI
    openapi_schema["components"]["securitySchemes"]["OAuth2PasswordBearer"] = {
        "type": "oauth2",
        "flows": {
            "password": {
                "tokenUrl": "/login",
                "scopes": {}
            }
        }
    }

    v3_app.openapi_schema = openapi_schema
    return v3_app.openapi_schema

v3_app.openapi = custom_openapi
v3_app.include_router(v3_reply_router, tags=["Chat v3"])

# redirect / to docs



# v3_app.include_router(agent_router, tags=["Agents"])