#!/usr/bin/env python3
"""
Proper text file to PDF converter using reportlab library.
This script converts a text file to a properly formatted PDF document.
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.utils import simpleSplit
from reportlab.lib.units import inch
import os
import sys

def txt_to_pdf(txt_file_path, pdf_file_path, page_size=letter, font_name="Helvetica", font_size=12):
    """
    Convert a text file to PDF format with proper formatting.
    
    Args:
        txt_file_path (str): Path to the input text file
        pdf_file_path (str): Path to the output PDF file
        page_size (tuple): Page size (default: letter)
        font_name (str): Font name (default: "Helvetica")
        font_size (int): Font size (default: 12)
    """
    
    # Check if input file exists
    if not os.path.exists(txt_file_path):
        raise FileNotFoundError(f"Input file not found: {txt_file_path}")
    
    # Read the text file with proper encoding handling
    try:
        with open(txt_file_path, "r", encoding="utf-8") as f:
            text = f.read()
    except UnicodeDecodeError:
        # Try with different encoding if UTF-8 fails
        with open(txt_file_path, "r", encoding="latin-1") as f:
            text = f.read()
    
    # Create a PDF canvas
    c = canvas.Canvas(pdf_file_path, pagesize=page_size)
    width, height = page_size
    
    # Set up text formatting parameters
    line_height = font_size + 2  # Add some line spacing
    margin_left = 0.75 * inch    # Left margin
    margin_right = 0.75 * inch   # Right margin
    margin_top = 1 * inch        # Top margin
    margin_bottom = 1 * inch     # Bottom margin
    
    max_width = width - margin_left - margin_right
    
    # Split text into lines that fit the page width
    lines = []
    for paragraph in text.split('\n'):
        if paragraph.strip():
            # Split long lines to fit page width
            wrapped_lines = simpleSplit(paragraph, font_name, font_size, max_width)
            lines.extend(wrapped_lines)
        else:
            lines.append("")  # Empty line for paragraph breaks
    
    # Calculate starting position
    y_position = height - margin_top
    
    # Write text to PDF
    for line in lines:
        # Check if we need a new page
        if y_position < margin_bottom:
            c.showPage()  # Start a new page
            y_position = height - margin_top
        
        # Set font and draw the line
        c.setFont(font_name, font_size)
        c.drawString(margin_left, y_position, line)
        y_position -= line_height
    
    # Save the PDF
    c.save()
    print(f"✅ PDF created successfully: {pdf_file_path}")
    print(f"📄 Converted {len(lines)} lines from {txt_file_path}")

def main():
    """
    Main function to handle command line usage or direct function calls.
    """
    
    # Example usage - you can modify these paths
    input_file = "ddd.txt"  # Change this to your input file
    output_file = "ddd.pdf"  # Change this to your desired output file
    
    # Check if files are provided as command line arguments
    if len(sys.argv) >= 3:
        input_file = sys.argv[1]
        output_file = sys.argv[2]
    elif len(sys.argv) == 2:
        input_file = sys.argv[1]
        # Generate output filename by replacing extension
        output_file = os.path.splitext(input_file)[0] + ".pdf"
    
    try:
        # Convert the text file to PDF
        txt_to_pdf(input_file, output_file)
        
        # Display file information
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"📊 Output file size: {file_size:,} bytes")
            
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print(f"💡 Make sure the file '{input_file}' exists in the current directory")
    except Exception as e:
        print(f"❌ An error occurred: {e}")

# Alternative function with more customization options
def advanced_txt_to_pdf(txt_file_path, pdf_file_path, **kwargs):
    """
    Advanced text to PDF converter with more customization options.
    
    Additional kwargs:
        - page_size: Page size (default: letter)
        - font_name: Font name (default: "Helvetica")
        - font_size: Font size (default: 12)
        - line_spacing: Extra line spacing (default: 2)
        - margins: Dict with 'top', 'bottom', 'left', 'right' margins in inches
    """
    
    # Default settings
    settings = {
        'page_size': letter,
        'font_name': "Helvetica",
        'font_size': 12,
        'line_spacing': 2,
        'margins': {'top': 1, 'bottom': 1, 'left': 0.75, 'right': 0.75}
    }
    
    # Update with user provided settings
    settings.update(kwargs)
    
    # Read the text file
    with open(txt_file_path, "r", encoding="utf-8") as f:
        text = f.read()
    
    # Create PDF
    c = canvas.Canvas(pdf_file_path, pagesize=settings['page_size'])
    width, height = settings['page_size']
    
    # Calculate margins
    margins = settings['margins']
    margin_left = margins['left'] * inch
    margin_right = margins['right'] * inch
    margin_top = margins['top'] * inch
    margin_bottom = margins['bottom'] * inch
    
    max_width = width - margin_left - margin_right
    line_height = settings['font_size'] + settings['line_spacing']
    
    # Process text
    lines = []
    for paragraph in text.split('\n'):
        if paragraph.strip():
            wrapped_lines = simpleSplit(paragraph, settings['font_name'], 
                                      settings['font_size'], max_width)
            lines.extend(wrapped_lines)
        else:
            lines.append("")
    
    # Write to PDF
    y_position = height - margin_top
    for line in lines:
        if y_position < margin_bottom:
            c.showPage()
            y_position = height - margin_top
        
        c.setFont(settings['font_name'], settings['font_size'])
        c.drawString(margin_left, y_position, line)
        y_position -= line_height
    
    c.save()
    print(f"✅ Advanced PDF created: {pdf_file_path}")

if __name__ == "__main__":
    main()
